<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>常住人口-人口情况</title>
  <script src="/Vue/vue.js"></script>
  <script src="/echarts/echarts.min.js"></script>
  <script src="/static/js/jslib/jquery-3.6.1.min.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <link rel="stylesheet" href="/elementui/css/elementui.css" />
  <script src="/elementui/js/elementui.js"></script>
  <style>
    .click-i {
      display: inline-block;
      width: 27px;
      height: 23px;
      margin: 0 5px;
      background: url('/static/images/common/header/click-1.png') no-repeat;
    }

    [v-cloak] {
      display: none;
    }

    * {
      margin: 0;
      padding: 0;
    }

    #app {
      width: 1070px;
      height: 1900px;
      background: url('/static/citybrain/csrk_3840/csrk_v2/img/back.png') no-repeat;
      background-size: 100% 100%;
      /* background-position: -22px -22px; */
      padding: 20px 30px;
      box-sizing: border-box;
    }

    .hearder_h1 {
      height: 70px;
      font-size: 38px;
      font-weight: 500;
      color: #ffffff;
      background: url('/static/citybrain/csrk_3840/csrk_v2/img/header.png') no-repeat;
    }

    .hearder_h1>span {
      margin-left: 50px;
    }

    .header-title2[data-v-4d0d1712] {
      width: 100% !important
    }

    .middle-con {
      position: relative;
      width: 100%;
      height: calc(100% - 100px);
      box-sizing: border-box;
      padding: 0 20px;
    }

    .clo_blue {
      color: #3d94be;
      font-weight: 700;
    }

    /* 下拉 */
    .select {
      display: inline-block;
      width: 205px;
      height: 40px;
      text-align: right;
      position: absolute;
      right: 36px;
      top: -10px;
      z-index: 100;
    }

    .flow-icon {
      width: 25px;
      position: absolute;
      top: 10px;
      right: 10px;
    }

    .flow-icon1 {
      margin-top: -5px;
      transform: rotateX(180deg);
    }

    .ul>div {
      width: 100%;
      height: 40px;
      line-height: 40px;
    }

    .select ul {
      width: 90%;
      height: 240px;
      text-align: center;
      font-size: 24px;
      color: #fefefe;
      overflow-y: auto;
      display: none;
      list-style: none;
      margin: 0 15px;
      padding: 0;
    }

    .select>span {
      display: block;
      font-size: 26px;
      color: #fff;
      position: absolute;
      top: -40px;
      left: 65px;
    }

    .ul {
      width: 100%;
      height: 40px;
      text-align: center;
      font-size: 26px;
      color: #fefefe;
      background-color: #132c4e;
      border: 1px solid #359cf8;
      border-radius: 40px;
      margin-top: 25px;
    }

    .select ul>li {
      width: 100%;
      height: 40px;
      line-height: 40px;
      background-color: #132c4ec2;
      box-sizing: border-box;
    }

    .select ul>li:hover,
    .select ul .li-active {
      background-color: #359cf8;
    }

    .ul-active {
      display: block !important;
    }

    .ul-active>li:last-of-type {
      border-radius: 0 0 20px 20px;
    }

    .select ul::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    .select ul::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 10px;
    }

    /* 搜索按钮 */
    .el-button--primary {
      width: 132px;
      height: 55px;
      font-size: 30px;
      position: absolute;
      right: 7px;
    }

    /* 表格 */
    .table {
      /* padding: 0 30px; */
      box-sizing: border-box;
      overflow-y: auto;
      margin-top: 20px;
    }

    .table .th {
      width: 100%;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 32px;
      line-height: 50px;
      background: #035d8d;
      color: #77b3f1;
    }

    .table .th_td {
      letter-spacing: 0px;
      text-align: center;
    }

    .table .tbody {
      width: 100%;
      height: calc(100% - 50px);
      /* overflow-y: auto; */
      overflow: hidden;
    }

    .table .tbody:hover {
      overflow-y: auto;
    }

    .table .tbody::-webkit-scrollbar {
      width: 4px;
      /*滚动条整体样式*/
      height: 4px;
      /*高宽分别对应横竖滚动条的尺寸*/
    }

    .tbody::-webkit-scrollbar {
      width: 4px;
      /*滚动条整体样式*/
      height: 4px;
      /*高宽分别对应横竖滚动条的尺寸*/
    }

    .tbody::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #20aeff;
      height: 8px;
    }

    .table .tbody::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #20aeff;
      height: 8px;
    }

    .table .tr {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 73px;
      line-height: 50px;
      font-size: 28px;
      color: #d6e7f9;
      background: #07335c;
      margin-top: 5px;
    }

    .table .tr:nth-child(2n) {
      background: #051c44;
    }

    .table .tr:nth-child(2n + 1) {
      background: #0e2f5a;
    }

    .table .tr:hover {
      background-color: #2055a5c9;
    }

    .active_table {
      background-color: #6990b6 !important;
    }

    .table .tr_td {
      overflow: hidden;
      white-space: nowrap;
      letter-spacing: 0px;
      text-align: center;
      box-sizing: border-box;
      text-overflow: ellipsis;
    }

    .table1 .tbody {
      max-height: 396px;
      min-height: 396px;
    }

    .open_see:hover {
      cursor: pointer;
      text-decoration: underline;
    }

    .disable_see {
      cursor: no-drop;
    }

    .empty {
      color: rgb(255, 255, 255);
      height: 395px;
      text-align: center;
      font-size: 34px;
      line-height: 395px;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <!-- 城市人口分析报告(月报) -->
    <div class="box" style="padding: 5px 0; position: relative; width: 100%">
      <nav style="margin: 10px 0 10px">
        <p class="hearder_h1">
          <span>城市活力分析报告(月报)</span>
        </p>
      </nav>
      <div class="box_con">
        <div class="middle-con">
          <div class="s-flex">
            <!-- <span class="s-font-45 clo_blue">筛选条件：</span> -->

            <div style="width: 310px">
              <span class="s-font-35 s-c-white">月份：</span>
              <div class="select" style="left: 110px; top: 2px" @click="showMoth=!showMoth">
                <div class="flow-icon" :class="showMoth?'flow-icon1':''">
                  <img src="/static/citybrain/hjbh/img/rkzt/up.png" alt="" width="25" />
                </div>
                <div class="ul" style="margin-top: 0">
                  <div style="cursor: pointer">{{startMoth}}</div>
                  <ul :class="[showMoth?'ul-active':'']">
                    <li style="cursor: pointer" v-for="(item,index) in mothList" @click="clickMoth(index,item)"
                      :class="item==startMoth?'li-active':''">
                      {{item}}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div style="width: 390px">
              <span class="s-font-35 s-c-white">县(市、区):</span>
              <div class="select" style="left: 510px; top: 2px" @click="showCity=!showCity">
                <div class="flow-icon" :class="showCity?'flow-icon1':''">
                  <img src="/static/citybrain/hjbh/img/rkzt/up.png" alt="" width="25" />
                </div>
                <div class="ul" style="margin-top: 0">
                  <div style="cursor: pointer">{{startCity}}</div>
                  <ul :class="[showCity?'ul-active':'']">
                    <li style="cursor: pointer" v-for="(item,index) in cityList" @click="clickCity(index,item)"
                      :class="item==startCity?'li-active':''">
                      {{item}}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <el-button @click="selectFun" type="primary" icon="el-icon-search" style="right: 145px;">搜索</el-button>
            <el-button @click="resetFun" type="primary" icon="el-icon-refresh">重置</el-button>
          </div>

          <div class="show_table">
            <div class="table table1 s-m-t-20">
              <div class="th">
                <div class="th_td" style="flex: 0.2">序号</div>
                <div class="th_td" style="flex: 0.6">月报名称</div>
                <div class="th_td" style="flex: 0.2">操作</div>
              </div>
              <div class="tbody" v-if="tableData.length>0">
                <div class="tr" v-for="(item ,i) in tableData">
                  <div class="tr_td" style="flex: 0.2">{{i<9?'0'+parseInt(i+1):i+1}} </div>
                      <div class="tr_td" style="flex: 0.6">{{item.name}}</div>
                      <div class="tr_td" style="flex: 0.2">
                        <!-- :class="item.name.indexOf('金华市')==-1&&item.name.indexOf('婺城区')==-1?'disable_see':'open_see'" -->
                        <span @click="openPdf(item)" class="open_see">
                          预览
                        </span>
                      </div>
                  </div>
                </div>
              </div>
              <div class="tbody" v-if="tableData.length==0">
                <div class="empty">暂无数据</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 城市人口分析报告(周报) -->
      <nav style="padding: 20px 0 10px">
        <p class="hearder_h1">
          <span>城市活力分析报告(周报)</span>
        </p>
      </nav>
      <div class="box_con">
        <div class="middle-con">
          <div class="s-flex">
            <!-- <span class="s-font-45 clo_blue">筛选条件：</span> -->

            <div style="width: 310px">
              <span class="s-font-35 s-c-white">周期：</span>
              <div class="select" style="left: 110px; top: 2px" @click="showWeek=!showWeek">
                <div class="flow-icon" :class="showWeek?'flow-icon1':''">
                  <img src="/static/citybrain/hjbh/img/rkzt/up.png" alt="" width="25" />
                </div>
                <div class="ul" style="margin-top: 0">
                  <div style="cursor: pointer">{{startWeek}}</div>
                  <ul :class="[showWeek?'ul-active':'']">
                    <li style="cursor: pointer" v-for="(item,index) in weekList" @click="clickWeek(index,item)"
                      :class="item==startWeek?'li-active':''">
                      {{item}}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div style="width: 390px">
              <span class="s-font-35 s-c-white">县(市、区):</span>
              <div class="select" style="left: 510px; top: 2px" @click="showCity1=!showCity1">
                <div class="flow-icon" :class="showCity1?'flow-icon1':''">
                  <img src="/static/citybrain/hjbh/img/rkzt/up.png" alt="" width="25" />
                </div>
                <div class="ul" style="margin-top: 0">
                  <div style="cursor: pointer">{{startCity1}}</div>
                  <ul :class="[showCity1?'ul-active':'']">
                    <li style="cursor: pointer" v-for="(item,index) in cityList" @click="clickCity1(index,item)"
                      :class="item==startCity1?'li-active':''">
                      {{item}}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <el-button @click="selectFun1" type="primary" icon="el-icon-search" style="right: 145px;">搜索</el-button>
            <el-button @click="resetFun1" type="primary" icon="el-icon-refresh">重置</el-button>
          </div>

          <div class="show_table">
            <div class="table table1 s-m-t-20">
              <div class="th">
                <div class="th_td" style="flex: 0.2">序号</div>
                <div class="th_td" style="flex: 0.6">周报名称</div>
                <div class="th_td" style="flex: 0.2">操作</div>
              </div>
              <div class="tbody" v-if="tableData1.length>0">
                <div class="tr" v-for="(item ,i) in tableData1">
                  <div class="tr_td" style="flex: 0.2">{{i<9?'0'+parseInt(i+1):i+1}} </div>
                      <div class="tr_td" style="flex: 0.6">{{item.name}}</div>
                      <div class="tr_td" style="flex: 0.2">
                        <!-- :class="item.name.indexOf('金华市')==-1&&item.name.indexOf('婺城区')==-1?'disable_see':'open_see'" -->
                        <span @click="openPdf(item)" class="open_see">
                          预览
                        </span>
                      </div>
                  </div>
                </div>
              </div>
              <div class="tbody" v-if="tableData1.length==0">
                <div class="empty">暂无数据</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 人口专题分析报告 -->
        <div style="position: relative">
          <nav style="margin: 10px 0 10px">
            <p class="hearder_h1">
              <span @click="openOnes()" style="cursor: pointer;">活力专题分析报告
                <i class="click-i"></i>
              </span>
            </p>
          </nav>
          <div class="box_con">
            <div class="middle-con">
              <div class="show_table">
                <div class="table table1 s-m-t-20">
                  <div class="th">
                    <div class="th_td" style="flex: 0.2">序号</div>
                    <div class="th_td" style="flex: 0.6">报告名称</div>
                    <div class="th_td" style="flex: 0.2">操作</div>
                  </div>
                  <div class="tbody" v-if="pdfData2.length>0">
                    <div class="tr" v-for="(item ,i) in pdfData2">
                      <div class="tr_td" style="flex: 0.2">{{i<9?'0'+parseInt(i+1):i+1}} </div>
                          <div class="tr_td" style="flex: 0.6">{{item.name}}</div>
                          <div class="tr_td" style="flex: 0.2">
                            <!-- :class="item.name.indexOf('2023年春节')==-1?'disable_see':'open_see'" -->
                            <span @click="openPdf(item)" class="open_see">
                              预览
                            </span>
                          </div>
                      </div>
                    </div>
                  </div>
                  <div class="tbody" v-if="pdfData2.length==0">
                    <div class="empty">暂无数据</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <script src="/static/js/jslib/axios.min.js"></script>
      <script src="/static/js/jslib/http.interceptor.js"></script>
      <script>
        let vm = new Vue({
          el: '#app',
          data: {
            showMoth: false,
            showWeek: false,
            index_moth: null,
            startMoth: '全部',
            startWeek: '全部',
            selectList01: [],
            weekList: ['全部', '43周'],
            mothList: ['全部', '1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            yearList: ['2020年', '2021年', '2022年'],
            showCity: false,
            showCity1: false,
            index_city: null,
            startCity: '全部',
            startCity1: '全部',
            cityList: [
              '全部',
              '婺城区',
              '金义新区',
              '兰溪市',
              '东阳市',
              '义乌市',
              '永康市',
              '浦江县',
              '武义县',
              '磐安县',
              '金华开发区',
            ],

            pdfData: [],
            pdfData1: [],
            pdfData2: [],
            tableData: [],
            tableData1: [],
            type: '',
            title: '',
          },
          mounted() {
            this.initList()
          },
          methods: {
            initList() {
              $api('/csrk_pdf_list').then(res => {
                this.tableData = this.pdfData = res.filter(e => e.type == '城市人口分析报告(月报)')
                this.tableData1 = this.pdfData1 = res.filter(e => e.type == '城市人口分析报告(周报)')
                this.pdfData2 = res.filter(e => e.type == '人口专题分析报告')
                console.log(this.tableData)
              })
            },
            closeMiddleIframe() {
              window.parent.lay.closeIframeByNames(['select_pdf'])
            },
            clickWeek(index, item) {
              this.startWeek = item
            },
            clickMoth(index, item) {
              this.startMoth = item
            },
            clickCity(index, item) {
              this.startCity = item
            },
            clickCity1(index, item) {
              this.startCity1 = item
            },
            selectFun() {
              if (this.startCity == '全部' && this.startMoth != '全部') {
                this.tableData = this.pdfData.filter((item) => {
                  if (this.startMoth == '2月') {
                    return item.name.indexOf(this.startMoth) != -1 && item.name.indexOf('12月') == -1
                  } else {
                    return item.name.indexOf(this.startMoth) != -1
                  }
                })
              } else if (this.startCity == '全部' && this.startMoth == '全部') {
                this.tableData = this.pdfData;
              } else if (this.startMoth == '全部' && this.startCity != '全部') {
                this.tableData = this.pdfData.filter((item) => {
                  return item.name.indexOf(this.startCity) != -1
                })
              }
              else {
                this.tableData = this.pdfData.filter((item) => {
                  if (this.startMoth == '2月') {
                    return item.name.indexOf(this.startMoth) != -1 && item.name.indexOf('12月') == -1 && item.name.indexOf(this.startCity) != -1
                  } else {
                    return item.name.indexOf(this.startMoth) != -1 && item.name.indexOf(this.startCity) != -1
                  }

                })
              }
            },
            resetFun() {
              this.startMoth = "全部";
              this.startCity = "全部";
              this.tableData = this.pdfData;
            },
            resetFun1() {
              this.startCity1 = "全部";
              this.startWeek = "全部";
              this.tableData1 = this.pdfData1;
            },
            selectFun1() {
              if (this.startCity1 == '全部' && this.startWeek != '全部') {
                this.tableData1 = this.pdfData1.filter((item) => {
                  return item.name.indexOf(this.startWeek) != -1
                })
              } else if (this.startCity1 == '全部' && this.startWeek == '全部') {
                this.tableData1 = this.pdfData1;
              } else if (this.startWeek == '全部' && this.startCity1 != '全部') {
                this.tableData1 = this.pdfData1.filter((item) => {
                  return item.name.indexOf(this.startCity1) != -1
                })
              }
              else {
                this.tableData1 = this.pdfData1.filter((item) => {
                  return item.name.indexOf(this.startWeek) != -1 && item.name.indexOf(this.startCity1) != -1
                })
              }
            },
            openOnes() {
              window.parent.lay.openIframe({
                type: 'openIframe',
                name: 'csrk_all_4',
                src: baseURL.url + `/static/citybrain/csrk_3840/commont2/csrk_all_4.html`,
                width: '100%',
                height: '100%',
                left: '0',
                top: '0',
                zIndex: '666',
                background: 'rgba(116,113,113,.4)',
              })
            },
            openPdf(item) {
              // if (item.name.indexOf('金华市') == -1 && item.name.indexOf('婺城区') == -1 && item.name.indexOf('2023年春节') == -1) return
              window.parent.lay.openIframe({
                type: 'openIframe',
                name: 'pdfPage',
                src: baseURL.url + '/static/citybrain/csrk_3840/commont/pdfPage.html',
                width: '110%',
                height: '100%',
                left: '-350px',
                top: '0',
                zIndex: '700',
                argument: { pdfPage: item },
                background: 'rgba(116,113,113,.4)',
              })
            },
          },
        })
      </script>
</body>

</html>